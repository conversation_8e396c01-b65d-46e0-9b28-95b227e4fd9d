package com.near_reality.plugins.area.osrs_home.npc

import com.near_reality.scripts.npc.actions.NPCActionScript
import com.zenyte.game.GameInterface
import com.zenyte.game.content.quests.QuestManager
import com.zenyte.game.content.quests.quests.AdventurersPath
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.dialogue.options

class PerkMasterNPCAction : NPCActionScript() {

    init {
        npcs(NpcId.FIGHTER_11681)
        "Talk-to" {
            player.dialogue(npc) {
                options("Perk Master Options") {
                    dialogueOption("Buy Perks", true) {
                        GameInterface.PERK_OVERVIEW.open(player)
                    }
                    dialogueOption("View Utility Tasks", true) {
                        GameInterface.UTILITY_TASKS.open(player)
                    }
                    dialogueOption("Item Sacrifice Values", true) {
                        GameInterface.SACRIFICE_PRICE_GUIDE.open(player)
                    }
                    dialogueOption("What are perks?", false) {
                        npc("Exiles offers 2 perk systems, Combat and Utility.")
                        npc("Combat Perks are enhancements that take combat to new heights. Unlock abilities such as faster attacks, more damage, life leech, and more!")
                        npc("Utility Perks are powerful QoL perks that will help all aspects of the game!")
                        player("How do I purchase the perks?")
                        npc("Each perk system uses their own currency, called Essence.")
                        npc("Combat Essence is gained by sacrificing valuable items to the Lava Pool right next to me. There is also a small chance of receiving some combat essence vouchers from PvM activities.")
                        npc("Utility Essence is gained by completing Utility Tasks, which are granted every 2 hours.").executeAction {
                            if (player.getTemporaryAttributes()["last hint arrow"] != null) {
                                player.packetDispatcher.resetHintArrow()
                            }
                            QuestManager.updateQuest(player, AdventurersPath::class.java, "TALK_TO_PERK_MASTER")
                            player.sendMessage("<col=ff0000>You've learned about the Perk Systems! Please return to the Exiles Guide.</col>")
                        }
                        return@dialogueOption
                    }

                }
            }
        }

        "Buy Perks" {
            if (player.getTemporaryAttributes()["last hint arrow"] != null) {
                player.packetDispatcher.resetHintArrow()
            }
            GameInterface.PERK_OVERVIEW.open(player)
        }

        "View Utility Tasks" {
            GameInterface.UTILITY_TASKS.open(player)
        }

        "Item Sacrifice Values" {
            GameInterface.SACRIFICE_PRICE_GUIDE.open(player)
        }
    }

}