package com.zenyte.game.content.essence.tasks;

import com.near_reality.game.content.shop.ShopCurrencyHandler;
import com.zenyte.constants.GFXConstants;
import com.zenyte.game.content.essence.tasks.type.CombatTask;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.shop.ShopCurrency;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.player.GameSetting;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.content.essence.tasks.type.Task;
import com.zenyte.game.content.essence.tasks.type.SkillingTask;
import com.zenyte.game.content.essence.tasks.reward.TaskReward;
import com.zenyte.game.content.essence.tasks.reward.RewardType;
import com.zenyte.game.world.entity.player.privilege.MemberRank;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;
import com.zenyte.utils.TimeUnit;
import it.unimi.dsi.fastutil.objects.Object2ObjectLinkedOpenHashMap;

import java.util.*;

import static com.zenyte.game.content.essence.tasks.TaskDifficulty.*;

/**
 * <AUTHOR> | 02/05/2019 | 22:36
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 * <AUTHOR> (Discord: astra4)
 */
public class TaskManager {

    private static final int UTILITY_TASK_SCROLL_RATE = 1000;
    private static final Item UTILITY_TASK_SCROLL = new Item(608, 1);

    private final transient Player player;
    Map<Task, TaskProgress> taskProgression;

    public TaskManager(final Player player) {
        this.player = player;
        this.taskProgression = new Object2ObjectLinkedOpenHashMap<>(3);
    }

    public void update(final Task challenge, final int progress) {
        final TaskProgress taskProgress = taskProgression.get(challenge);
        if (taskProgress == null || taskProgress.isCompleted()) {
            return;
        }
        taskProgress.progress(progress);
        check(challenge);
    }

    public void update(final Task challenge) {
        update(challenge, 1);
    }

    public void checkCombatUpdate(Player player, NPC npc) {
        final String killedNpcName = npc.getName(player).toLowerCase();

        for (Map.Entry<Task, TaskProgress> entry : player.getTaskManager().getTaskProgression().entrySet()) {
            Task task = entry.getKey();

            if (!(task instanceof CombatTask combatTask)) {
                continue;
            }

            final String taskNpc = combatTask.getNpc().toLowerCase();

            if (taskNpc.equals(killedNpcName)) {
                update(task);
                return;
            }
        }
    }

    private void check(final Task challenge) {
        final TaskProgress progress = taskProgression.get(challenge);
        if (progress == null) {
            return;
        }
        if (progress.getProgress() == challenge.getLength()) {
            player.sendMessage("<img=13><col=00FF00><shad=000000>Utility Task complete: " + challenge.getName() + "! Return to the Perk Master at home to claim your reward!");
            player.setGraphics(GFXConstants.BIG_FIREWORKS);
        } else {
            if (player.getNumericAttribute(GameSetting.UTILITY_TASK_NOTIFICATIONS.toString()).intValue() == 1) {
                player.sendMessage("<img=13><col=ce8500><shad=000000>Utility Task updated: " + progress);
            }
        }
    }

    public void assignRandomChallenge() {
        final Task challenge = getRandomChallenge();
        if (challenge != null)
            assignChallenge(challenge);
    }

    public void assignChallenge(final Task challenge) {
        if(!hasAvailableSlots()) {
            return;
        }
        taskProgression.put(challenge, new TaskProgress(challenge));
        player.sendMessage("<img=13><col=ce8500><shad=000000>New Utility Task: " + taskProgression.get(challenge));
    }

    private Task getRemovableChallenge() {
        final Iterator<Map.Entry<Task, TaskProgress>> iterator = taskProgression.entrySet().iterator();
        if (iterator.hasNext()) {
            return iterator.next().getKey();
        }
        return null;
    }

    public Task getRandomChallenge() {
        final TaskCategory preferredCategory = TaskCategory.SKILLING;

        Task challenge = null;
        int retries = 5;

        for (int i = 0; i < retries && challenge == null; i++) {
            challenge = getRandomChallengeFromCategory(preferredCategory);
        }

        if (challenge != null) {
            return challenge;
        }

        // As a last resort, return any unassigned task regardless of difficulty
        List<Task> leftovers = new ArrayList<>();
        for (Task c : TaskWrapper.challenges.values()) {
            if (!taskProgression.containsKey(c)) {
                leftovers.add(c);
            }
        }
        return leftovers.isEmpty() ? null : Utils.random(leftovers);
    }

    private Task getRandomChallengeFromCategory(TaskCategory category) {
        TaskDetails information = category.getDetails(player);
        List<Task> candidates = new ArrayList<>();
        for (Task c : TaskWrapper.challenges.values()) {
            if (taskProgression.containsKey(c)) {
                continue;
            }
            if (!c.getCategory().equals(category)) {
                continue;
            }
            if (c.getCategory().equals(TaskCategory.SKILLING)) {
                int taskSkill = ((SkillingTask) c).getSkill();
                int playerLevel = player.getSkills().getLevelForXp(taskSkill);

                TaskDifficulty difficulty = playerLevel >= 85 ? ELITE
                        : playerLevel >= 60 ? HARD
                        : playerLevel >= 30 ? MEDIUM
                        : EASY;

                if (!c.getDifficulty().equals(difficulty)) {
                    continue;
                }
            }
            candidates.add(c);
        }
        if(player.isDebugging) {
            for(Task task : candidates) {
                player.sendMessage("Available Task: " + task.getDifficulty() + " " + task.getName());
            }
        }
        return candidates.isEmpty() ? null : Utils.random(candidates);
    }

    public Task getChallenge(final int index) {
        final Set<Map.Entry<Task, TaskProgress>> set = taskProgression.entrySet();
        final Object[] array = set.toArray();
        if (index >= array.length) {
            return null;
        }
        final Map.Entry<Task, TaskProgress> entry = (Map.Entry<Task, TaskProgress>) array[index];
        return entry.getKey();
    }

    public TaskProgress getProgress(final Task challenge) {
        return taskProgression.get(challenge);
    }

    public void reject(final Task challenge) {
        taskProgression.remove(challenge);
        player.sendMessage("<img=13><col=FF0000><shad=000000>Utility Task cancelled: " + challenge.getName());
    }

    public boolean claim(final Task challenge) {
        final TaskProgress progress = taskProgression.get(challenge);
        if (progress == null || !progress.isCompleted()) {
            player.sendMessage("This task is not ready to be claimed.");
            return false;
        }
        final TaskReward[] rewards = challenge.getRewards();
        int space = 1;
        for (final TaskReward reward : rewards) {
            if (reward.getType().equals(RewardType.ITEM)) {
                space += 1;
            }
        }
        if (!player.getInventory().checkSpace(space)) {
            player.sendMessage("Not enough space in inventory to claim the challenge.");
            return false;
        }
        player.sendMessage("<img=13><col=00FF00><shad=000000>Utility Task redeemed: " + challenge.getName());
        StringBuilder stringBuilder = new StringBuilder("<img=13><col=00FF00><shad=000000>You have been awarded with ");
        for (final TaskReward reward : rewards) {
            stringBuilder.append(reward.apply(player));
        }

        Item coins = new Item(995, challenge.getDifficulty().getCoins());
        if(coins.getAmount() > 0) {
            player.getInventory().addItem(coins);
            stringBuilder.append(Utils.formatNumWDot(coins.getAmount())).append(" ").append(coins.getName()).append(" , and ");
        }

        int utilityEssence = challenge.getDifficulty().getUtilityEssence();
        if(utilityEssence > 0) {
            ShopCurrencyHandler.add(ShopCurrency.UTILITY_ESSENCE, player, utilityEssence);
            stringBuilder.append(Utils.formatNumberWithCommas(utilityEssence)).append(" Utility Essence!");
        }

        player.sendMessage(String.valueOf(stringBuilder));

        taskProgression.remove(challenge);
        return true;
    }

    public void notifyUnclaimedChallenges() {
        int unclaimedChallenges = 0;
        for (final Map.Entry<Task, TaskProgress> entry : taskProgression.entrySet()) {
            final TaskProgress progress = entry.getValue();
            if (progress.isCompleted() && !progress.isClaimed()) {
                unclaimedChallenges++;
            }
        }
        if (unclaimedChallenges > 0) {
            player.sendMessage("<img=13><col=00FF00><shad=000000>You seem to have one or more completed utility tasks. Return to the Perk Master at Home to claim your rewards!");
        }
    }

    public boolean hasAvailableSlots() {
        if(taskProgression.size() >= getMaximumTasks()) {
            player.sendMessage("<img=13><col=ce8500><shad=000000>You would have received a new Utility Task, but you have no empty slots left!");
            return false;
        }
        return true;
    }

    public int getAvailableSlots() {
        return Math.max(0, getMaximumTasks() - taskProgression.size());
    }

    private int getMaximumTasks() {
        if (player.getPrivilege().inherits(PlayerPrivilege.ADMINISTRATOR)) {
            return 6;
        }
        if (player.getMemberRank().equalToOrGreaterThan(MemberRank.EPIC)) {
            return 6;
        } else if (player.getMemberRank().equalToOrGreaterThan(MemberRank.EXTREME)) {
            return 5;
        } else if (player.getMemberRank().equalToOrGreaterThan(MemberRank.SUPER)) {
            return 4;
        }
        return 3;
    }

    public void rollUtilityTaskScroll() {
        long now = System.currentTimeMillis();
        Long lastRoll = (Long) player.getAttributes().get("roll_utility_delay");

        if (lastRoll == null || now - lastRoll >= TimeUnit.TICKS.toMillis(3)) {
            player.getAttributes().put("roll_utility_delay", now);

            if (Utils.random(UTILITY_TASK_SCROLL_RATE) == 0) {
                player.sendMessage("<img=13><col=00FF00><shad=000000>You find a Utility task scroll while skilling!");
                player.getInventory().addItem(UTILITY_TASK_SCROLL).onFailure(remainder -> {
                    player.sendMessage("There was no room in your inventory, so " + remainder.getName() + " has been added to your bank!");
                    player.getBank().add(remainder).onFailure(remainder2 -> {
                        player.sendMessage("There was no room in your bank, so " + remainder2.getName() + " has been dropped to the floor!");
                        World.spawnFloorItem(remainder2, player);
                    });
                });
            }
        } else {
            if(player.isDebugging)
                player.sendMessage("Skipping utility roll because too fast");
        }
    }



    public Map<Task, TaskProgress> getTaskProgression() {
        return taskProgression;
    }

    public void setTaskProgression(Map<Task, TaskProgress> taskProgression) {
        this.taskProgression = taskProgression;
    }
}
