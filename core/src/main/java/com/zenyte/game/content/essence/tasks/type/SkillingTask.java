package com.zenyte.game.content.essence.tasks.type;

import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.content.essence.tasks.TaskCategory;
import com.zenyte.game.content.essence.tasks.TaskDifficulty;
import com.zenyte.game.content.essence.tasks.reward.TaskReward;
import com.zenyte.game.content.essence.tasks.reward.impl.ExperienceReward;
import org.jetbrains.annotations.NotNull;

import static com.zenyte.game.content.essence.tasks.TaskDifficulty.*;

/**
 * <AUTHOR> | 03/05/2019 | 22:38
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>
 */
public enum SkillingTask implements Task {

    /**
     * DO NOT CHANGE THE NAMES OF THE ENUM ENTRIES AS IT MIGHT FUCK UP DESERIALIZATION FOR EXISTING CHARACTERS
     */
    CRAFT_GOLD_BRACELETS("Craft Gold bracelets", EASY, 25, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 700)),
    CRAFT_LEATHER_BODIES("Craft Leather Bodies", EASY, 10, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 1400)),
    CRAFT_EMERALD_BRACELETS("Craft Emerald Bracelets", MEDIUM, 10, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 3000)),
    CRAFT_TOPAZ_AMULETS("Craft Topaz Amulets", MEDIUM, 5, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 4500)),
    CRAFT_UNPOWERED_ORBS("Craft Unpowered Orbs", MEDIUM, 100, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 4600)),
    CRAFT_GREEN_DRAGONHIDE_CHAPS("Craft Green Dragonhide Chaps", HARD, 10, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 6000)),
    CRAFT_DIAMOND_AMULETS("Craft Diamond Amulets", HARD, 5, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 7000)),
    CRAFT_BLACK_DRAGONHIDE_CHAPS("Craft Black Dragonhide Chaps", ELITE, 10, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 8200)),
    CRAFT_EMPTY_LIGHT_ORBS("Craft Empty Light Orbs", ELITE, 200, SkillConstants.CRAFTING, new ExperienceReward(SkillConstants.CRAFTING, 8700)),

    CLEAN_GUAM_LEAFS("Clean Grimy Guam Leafs", EASY, 35, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 300)),
    MAKE_COMPOST_POTIONS("Make Compost Potions", EASY, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 2200)),
    CLEAN_TOADFLAX("Clean Grimy Toadflax", MEDIUM, 35, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 3000)),
    MAKE_PRAYER_POTIONS("Make Prayer Potions", MEDIUM, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 3800)),
    MAKE_SUPER_ANTIPOISON_POTIONS("Make Super Antipoison Potions", MEDIUM, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 4800)),
    CLEAN_SNAPDRAGONS("Clean Grimy Snapdragons", HARD, 35, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 5900)),
    MAKE_RANGING_POTIONS("Make Ranging Potions", HARD, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 7200)),
    MAKE_SARADOMIN_BREWS("Make Saradomin Brews", ELITE, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 8100)),
    MAKE_SUPER_COMBAT_POTIONS("Make Super Combat Potions", ELITE, 20, SkillConstants.HERBLORE, new ExperienceReward(SkillConstants.HERBLORE, 9000)),

    MINE_ESSENCE("Mine Rune/Pure Essence", EASY, 100, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 300)),
    MINE_IRON_ORES("Mine Iron ores", EASY, 50, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 1500)),
    MINE_COAL("Mine Coal", MEDIUM, 50, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 3000)),
    MINE_SANDSTONE("Mine Sandstone Rocks", MEDIUM, 100, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 3500)),
    MINE_GOLD_ORES("Mine Gold Ore", MEDIUM, 50, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 4000)),
    MINE_MITHRIL_ORES("Mine Mithril ores", HARD, 50, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 5500)),
    MINE_ADAMANTITE_ORES("Mine Adamantite Ores", HARD, 25, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 7000)),
    MINE_RUNITE_ORES("Mine Runite Ores", ELITE, 25, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 8500)),
    MINE_AMETHYST("Mine Amethyst", ELITE, 50, SkillConstants.MINING, new ExperienceReward(SkillConstants.MINING, 9200)),

    CRAFT_WATER_RUNES("Craft Water Runes", EASY, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 500)),
    CRAFT_FIRE_RUNES("Craft Fire Runes", EASY, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 1400)),
    CRAFT_COSMIC_RUNES("Craft Cosmic Runes", MEDIUM, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 2700)),
    CRAFT_CHAOS_RUNES("Craft Water Runes", MEDIUM, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 3500)),
    CRAFT_NATURE_RUNES("Craft Nature Runes", MEDIUM, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 4400)),
    CRAFT_LAW_RUNES("Craft Law Runes", HARD, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 5400)),
    CRAFT_DEATH_RUNES("Craft Death Runes", HARD, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 6500)),
    CRAFT_BLOOD_RUNES("Craft Water Runes", ELITE, 200, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 7700)),
    CRAFT_SOUL_RUNES("Craft Soul Runes", ELITE, 150, SkillConstants.RUNECRAFTING, new ExperienceReward(SkillConstants.RUNECRAFTING, 9000)),

    HARVEST_CABBAGES("Harvest Cabbages", EASY, 50, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 700)),
    HARVEST_MARRENTILLS("Harvest Marrentills", EASY, 35, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 1400)),
    CHECK_HEALTH_APPLE_TREES("Check-health of apple trees", MEDIUM, 2, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 2700)),
    HARVEST_RANARRS("Harvest Ranarrs", MEDIUM, 35, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 3200)),
    HARVEST_WATERMELONS("Harvest Watermelons", MEDIUM, 50, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 4700)),
    HARVEST_SNAPDRAGONS("Harvest Snapdragons", HARD, 35, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 6200)),
    CHECK_HEALTH_PALM_TREES("Check-health of palm trees", HARD, 2, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 6800)),
    CHECK_HEALTH_DRAGONFRUIT_TREES("Check-health of dragonfruit trees", ELITE, 2, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 8100)),
    CHECK_HEALTH_REDWOOD_TREES("Check-health of redwood trees", ELITE, 1, SkillConstants.FARMING, new ExperienceReward(SkillConstants.FARMING, 9000)),

    COOK_HERRING("Cook Herring", EASY, 25, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 500)),
    COOK_TROUT("Cook Trouts", EASY, 25, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 1500)),
    COOK_TUNA("Cook Tuna", MEDIUM, 50, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 3000)),
    COOK_WINES("Make Wines", MEDIUM, 50, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 3500)),
    COOK_LOBSTERS("Cook Lobsters", MEDIUM, 50, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 4000)),
    COOK_SWORDFISH("Cook Swordfish", MEDIUM, 50, SkillConstants.COOKING,  new ExperienceReward(SkillConstants.COOKING, 4500)),
    COOK_MONKFISH("Cook Monkfish", HARD, 75, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 6200)),
    COOK_KARAMBWAN("Cook Karambwan", HARD, 75, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 6200)),
    COOK_SHARK("Cook Shark", ELITE, 100, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 7200)),
    COOK_ANGLERFISH("Cook Anglerfish", ELITE, 100, SkillConstants.COOKING, new ExperienceReward(SkillConstants.COOKING, 8400)),

    PICKPOCKET_HAM_MEMBERS("Pickpocket H.A.M Members", EASY, 20, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 1500)),
    PICKPOCKET_FRUIT_STALL("Steal from a fruit stall", EASY, 20, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 2500)),
    PICKPOCKET_MASTER_FARMERS("Pickpocket Master Farmers", MEDIUM, 40, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 3800)),
    PICKPOCKET_ARDOUGNE_KNIGHTS("Pickpocket Ardougne Knights", MEDIUM, 40, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 5500)),
    PICKPOCKET_PALADINS("Pickpocket Paladins", HARD, 50, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 7000)),
    PICKPOCKET_HEROES("Pickpocket Heroes", HARD, 50, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 8000)),
    PICKPOCKET_ELVES("Pickpocket Elves", ELITE, 50, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 8500)),
    PICKPOCKET_TZAAR_HUR("Pickpocket Tzhaar-hur", ELITE, 50, SkillConstants.THIEVING, new ExperienceReward(SkillConstants.THIEVING, 9000)),

    COMPLETE_LAPS_DRAYNOR_VILLAGE_COURSE("Complete laps of the Draynor village course", EASY, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 1000)),
    COMPLETE_LAPS_AL_KHARID_COURSE("Complete laps of the Al Kharid course", EASY, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 2000)),
    COMPLETE_LAPS_VARROCK_COURSE("Complete laps of the Varrock Rooftop course", MEDIUM, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 3000)),
    COMPLETE_LAPS_CANIFIS_COURSE("Complete laps of Canifis course", MEDIUM, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 4000)),
    COMPLETE_LAPS_SEERS_COURSE("Complete laps of the Seers Rooftop course", HARD, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 6000)),
    COMPLETE_LAPS_POLLNIVNEACH_COURSE("Complete laps of the Pollnivneach course", HARD, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 7000)),
    COMPLETE_LAPS_RELLEKKA_COURSE("Complete laps of the Rellekka course", ELITE, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 8000)),
    COMPLETE_LAPS_ARDOUGNE_COURSE("Complete laps of the Ardougne course", ELITE, 5, SkillConstants.AGILITY, new ExperienceReward(SkillConstants.AGILITY, 9000)),

    COMPLETE_TURAEL_ASSIGNMENTS("Complete Assignments from Turael", EASY, 2, SkillConstants.SLAYER, new ExperienceReward(SkillConstants.SLAYER, 1000)),
    COMPLETE_VANNAKA_ASSIGNMENTS("Complete Assignments from Vannaka", MEDIUM, 2, SkillConstants.SLAYER, new ExperienceReward(SkillConstants.SLAYER, 3000)),
    COMPLETE_NIEVE_ASSIGNMENTS("Complete Assignments from Nieve", HARD, 2, SkillConstants.SLAYER, new ExperienceReward(SkillConstants.SLAYER, 6000)),
    COMPLETE_DURADEL_ASSIGNMENTS("Complete Assignments from Duradel", ELITE, 2, SkillConstants.SLAYER, new ExperienceReward(SkillConstants.SLAYER, 8500)),

    SMELT_BRONZE_BARS("Smelt Bronze Bars", EASY, 50, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 300)),
    SMELT_IRON_BARS("Smelt Iron Bars", EASY, 100, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 1500)),
    SMELT_STEEL_BARS("Smelt Steel Bars", MEDIUM, 100, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 3000)),
    SMELT_GOLD_BARS("Smelt Gold Bars", MEDIUM, 100, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 4000)),
    SMITH_STEEL_PLATEBODIES("Smith Steel Platebodies", MEDIUM, 10, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 4800)),
    SMITH_MITHRIL_FULL_HELMETS("Smith Mithril Full Helmets", HARD, 20, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 5700)),
    SMELT_ADAMANT_BARS("Smelt Adamant Bars", HARD, 100, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 7000)),
    SMELT_RUNITE_BARS("Smelt Runite Bars", ELITE, 30, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 8500)),
    SMITH_RUNE_SCIMITARS("Smith Rune Scimitars", ELITE, 10, SkillConstants.SMITHING, new ExperienceReward(SkillConstants.SMITHING, 9000)),

    FLETCH_LONGBOWS("Fletch Longbows", EASY, 20, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 1000)),
    FLETCH_IRON_DARTS("Fletch Iron Darts", EASY, 250, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 2200)),
    FLETCH_WILLOW_SHORTBOWS("Fletch Willow Shortbows", MEDIUM, 20, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 3500)),
    FLETCH_STEEL_DARTS("Fletch Steel darts", MEDIUM, 250, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 3700)),
    FLETCH_MITHRIL_ARROWS("Fletch Mithril Arrows", MEDIUM, 300, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 4500)),
    FLETCH_BROAD_BOLTS("Fletch Broad Bolts", HARD, 300, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 5500)),
    FLETCH_YEW_LONGBOWS("Fletch Yew Longbows", HARD, 20, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 7000)),
    FLETCH_DRAGON_BOLTS("Fletch Dragon Bolts", ELITE, 50, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 8400)),
    FLETCH_DRAGON_DARTS("Fletch Dragon Darts", ELITE, 40, SkillConstants.FLETCHING, new ExperienceReward(SkillConstants.FLETCHING, 9000)),

    CATCH_GOLDEN_WARBLERS("Catch Golden Warblers", EASY, 5, SkillConstants.HUNTER, new ExperienceReward(SkillConstants.HUNTER, 500)),
    CATCH_ORANGE_SALAMANDERS("Catch Orange Salamanders", MEDIUM, 20, SkillConstants.HUNTER, new ExperienceReward(SkillConstants.HUNTER, 3500)),
    CATCH_RED_CHINCHOMPAS("Catch Red Chinchompas", HARD, 30, SkillConstants.HUNTER, new ExperienceReward(SkillConstants.HUNTER, 6300)),
    CATCH_BLACK_CHINCHOMPAS("Catch Black Chinchompas", ELITE, 30, SkillConstants.HUNTER, new ExperienceReward(SkillConstants.HUNTER, 7300)),

    CHOP_NORMAL_LOGS("Chop Normal Logs", EASY, 20, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 300)),
    CHOP_OAK_LOGS("Chop Oak Logs", EASY, 20, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 1500)),
    CHOP_WILLOW_LOGS("Chop Willow Logs", MEDIUM, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 3000)),
    CHOP_TEAK_LOGS("Chop Teak Logs", MEDIUM, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 3500)),
    CHOP_MAPLE_LOGS("Chop Maple Logs", MEDIUM, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 4500)),
    CHOP_YEW_LOGS("Chop Yew Logs", HARD, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 6000)),
    CHOP_MAGIC_LOGS("Chop Magic Logs", HARD, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 7500)),
    CHOP_REDWOOD_LOGS("Chop Redwood Logs", ELITE, 50, SkillConstants.WOODCUTTING, new ExperienceReward(SkillConstants.WOODCUTTING, 9000)),

    CATCH_KARAMBWANJI("Catch Karambwanji", EASY, 100, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 500)),
    CATCH_TROUT("Catch Trout", EASY, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 2000)),
    CATCH_TUNA("Catch Tuna", MEDIUM, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 3500)),
    CATCH_LOBSTERS("Catch Lobsters", MEDIUM, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 4000)),
    CATCH_BASS("Catch Bass", MEDIUM, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 4600)),
    CATCH_MONKFISH("Catch Monkfish", HARD, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 6200)),
    CATCH_KARAMBWAN("Catch Karambwan", HARD, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 6800)),
    CATCH_SHARKS("Catch Sharks", HARD, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 7600)),
    CATCH_ANGLERFISH("Catch Anglerfish", ELITE, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 8200)),
    CATCH_DARK_CRABS("Catch Dark Crabs", ELITE, 50, SkillConstants.FISHING, new ExperienceReward(SkillConstants.FISHING, 8500)),

    BURN_OAK_LOGS("Burn Oak Logs", EASY, 20, SkillConstants.FIREMAKING, new ExperienceReward(SkillConstants.FIREMAKING, 1500)),
    BURN_TEAK_LOGS("Burn Teak Logs", MEDIUM, 30, SkillConstants.FIREMAKING, new ExperienceReward(SkillConstants.FIREMAKING, 3500)),
    BURN_MAGIC_LOGS("Burn Magic Logs", HARD, 40, SkillConstants.FIREMAKING, new ExperienceReward(SkillConstants.FIREMAKING, 7500)),
    BURN_REDWOOD_LOGS("Burn Redwood Logs", ELITE, 50, SkillConstants.FIREMAKING, new ExperienceReward(SkillConstants.FIREMAKING, 9000)),

    ;

    private final String name;
    private final TaskDifficulty difficulty;
    private final TaskReward[] rewards;
    private final int length;
    private final int skill;

    SkillingTask(final String name, final TaskDifficulty difficulty, final int length, final int skill, final TaskReward... rewards) {
        this.name = name;
        this.difficulty = difficulty;
        this.length = length;
        this.skill = skill;
        this.rewards = rewards;
    }

    public static final SkillingTask[] all = values();

    @NotNull
    @Override
    public String getName() {
        return name;
    }

    @NotNull
    @Override
    public TaskCategory getCategory() {
        return TaskCategory.SKILLING;
    }

    @NotNull
    @Override
    public TaskDifficulty getDifficulty() {
        return difficulty;
    }

    @NotNull
    @Override
    public TaskReward[] getRewards() {
        return rewards;
    }

    @Override
    public int getLength() {
        return length;
    }

    public int getSkill() {
        return skill;
    }
}
