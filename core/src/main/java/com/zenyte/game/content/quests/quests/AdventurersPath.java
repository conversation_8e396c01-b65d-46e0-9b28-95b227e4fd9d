package com.zenyte.game.content.quests.quests;

import com.zenyte.game.content.essence.combat.CombatPerkWrapper;
import com.zenyte.game.content.quests.*;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.HintArrow;
import com.zenyte.game.model.HintArrowPosition;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.near_reality.game.item.CustomItemId;

import java.util.*;
import java.util.function.Predicate;

import static com.zenyte.game.content.quests.QuestStage.*;



/**
 * Adventurer's Path Quest Implementation
 *
 * OBJECTIVE LENGTH DEFINITION:
 * Each objective must specify its length as the second parameter:
 * - OBJECTIVE_NAME(STAGE, LENGTH, "Description")
 * - LENGTH = 1 for single-step objectives (most common)
 * - LENGTH = N for multi-step objectives (e.g., 6 for killing 6 different <PERSON><PERSON> brothers)
 *
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 *
 * USAGE EXAMPLES:
 *
 * // Simple quest update (call from your existing game systems):
 * AdventurersPath.onSlayerObjectiveCompleted(player, slayerMasterId);
 * AdventurersPath.onCombatSpeedPerkUnlocked(player);
 * AdventurersPath.onBarrowsBrothersKilled(player, brotherNpcId);
 * AdventurersPath.onJadDefeatedFireCapeReceived(player);
 *
 * // Manual quest updates (if needed):
 * QuestManager.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
 *
 * // Check quest progress:
 * if (AdventurersPath.canStartAdventurersPath(player)) { ... }
 * String progress = AdventurersPath.getQuestProgressSummary(player);
 *
 * // NPCs are automatically collected from getDialogueNpc() and getObjectiveNpcs() methods:
 * int[] questNpcs = AdventurersPath.TALK_TO_EXILES_GUIDE.getQuestNpcs(); // Returns all dialogue NPCs
 *
 * // For objectives with multiple dialogue NPCs, override getObjectiveNpcs():
 * // Only include NPCs you need to TALK TO, not combat NPCs
 */
public enum AdventurersPath implements Quest {

    TALK_TO_EXILES_GUIDE(NOT_STARTED, 1,
                         "Talk to the Exiles Guide.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        npc("Welcome to Exiles, "+player.getName()+"! I see you've completed the tutorial. It is time for you to embark on the Adventurer's Path quest. This quest will help you learn the ways of the adventurer!");
                        npc("Your first objective is to talk to the Perk Master North of Edgeville (near the lava pool) to learn about our perk system here at Exiles. Ask him about perks!");
                        QuestManager.updateQuest(player, AdventurersPath.class, "TALK_TO_EXILES_GUIDE");
                        World.findNPC(NpcId.FIGHTER_11681, player.getLocation(), 20).ifPresent(npc -> player.getPacketDispatcher().sendHintArrow(new HintArrow(npc)));
                    }
                });
            }
        }
    },

    // Objective 1: Talk to the Perk Master
    TALK_TO_PERK_MASTER(IN_PROGRESS, 2, "Learn About Perk System.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);

                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0) { //Slayer task not completed
                            npc("Have you went and talked with the Perk Master yet? Remember, the Perk Master can be found North of Edgeville (near the lava pool). Talk to him and ask him what perks are.");
                        } else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "TALK_TO_PERK_MASTER");
                            npc("Great job! Now you have learned about how our Perk system works. Your next objective may allow you to earn some combat essence vouchers!");
                            npc("Your second objective is to complete a Slayer task from Turael. He can be found at ::slayer (in the building NE of home). Speak to him for your task!");
                            World.findNPC(NpcId.TURAEL, player.getLocation(), 40).ifPresent(npc -> player.getPacketDispatcher().sendHintArrow(new HintArrow(npc)));
                        }
                    }
                });
            }
        }
    },

    // Objective 2: Complete a Slayer Task from Turael
    COMPLETE_SLAYER_TASK_TURAEL(IN_PROGRESS, 2, "Complete a Slayer Task From Turael.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);

                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0) { //Slayer task not completed
                            npc("Have you received and completed a Slayer task from Turael yet? Remember, Turael can be found at ::slayer in the building northeast of home. Complete your task and return to me when you're done!");
                        } else {
                            // Task completed - next objective dialogue (Rewards given here)
                            QuestManager.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
                            npc("Excellent! You've proven yourself capable in combat. Slayer task will be essential for your growth.");
                            npc("Your third objective is to sacrifice your 100 Combat Essence vouchers I just gave you in order to unlock one of the 3 combat speed perks.");
                            final HintArrow arrow = new HintArrow(3088, 3509, (byte) 100, HintArrowPosition.CENTER);
                            player.getPacketDispatcher().sendHintArrow(arrow);
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            player.getInventory().addOrDrop(new Item(ItemId.ANTIQUE_LAMP_13145, 3)); // Easy diary lamps
            player.getInventory().addOrDrop(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 3 experience lamps and 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 3: Sacrifice Combat Essence vouchers and unlock one of the 3 combat speed perks
    UNLOCK_FIRST_COMBAT_SPEED_PERK(IN_PROGRESS, 3, "Unlock First Combat Speed Perk.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0)
                            npc("You still need to sacrifice the 100 Combat Essence vouchers I just gave you and unlock one of the 3 combat speed perks. This can be done at the Combat Essence Lava Pool to the North of Edgeville.");
                        else if (progress == 1)
                            npc("I see you have sacrificed your Combat Essence vouchers but you still need to purchase one of the 3 combat speed perks. Go to the Perk Master up north of Edgeville to purchase a combat perk.");
                        else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FIRST_COMBAT_SPEED_PERK");
                            npc("I see you've unlocked your first combat speed perk! These perks will greatly enhance your combat effectiveness. Take these dragon bones to help with your Prayer training.");
                            npc("Your fourth objective is to kill all 6 Barrows brothers. You may need to train up a bit before taking this on!");
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            player.getInventory().addOrDrop(new Item(537, 20)); // Dragon bones
            player.sendMessage("<col=00ff00>Quest Reward: You received 20 dragon bones!</col>");
        }
    },

    // Objective 4: Kill 1 of each of the barrows brothers (6 brothers total)
    KILL_ALL_BARROWS_BROTHERS(IN_PROGRESS, 2, "Kill All The Barrows Brothers.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0) {
                            int[] brotherIds = {1672, 1673, 1674, 1675, 1676, 1677};
                            String[] brotherNames = {"Ahrim", "Dharok", "Guthan", "Karil", "Torag", "Verac"};
                            List<String> remaining = new ArrayList<>();
                            for (int i = 0; i < brotherIds.length; i++) {
                                if (!player.getBooleanAttribute("barrows_brother_killed_" + brotherIds[i])) {
                                    remaining.add(brotherNames[i]);
                                }
                            }
                            if (remaining.isEmpty()) {
                                npc("You've already killed all of the Barrows brothers. Talk to me again if this seems incorrect.");
                            } else {
                                npc("You still need to kill the following Barrows brothers: " + String.join(", ", remaining));
                            }
                        } else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS");
                            npc("Incredible! You've defeated all the Barrows brothers! Your combat prowess is truly impressive. Take these Combat Essence Vouchers to unlock another combat speed perk.");
                            npc("Your fifth objective is to unlock another of the remaining 2 combat speed perks. I have given you another 100 Combat Essence Vouchers to help you purchase a new speed perk.");
                            final HintArrow arrow = new HintArrow(3088, 3509, (byte) 100, HintArrowPosition.CENTER);
                            player.getPacketDispatcher().sendHintArrow(arrow);
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            player.getInventory().addOrDrop(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 5: Unlock another of the remaining 2 combat speed perks
    UNLOCK_SECOND_COMBAT_SPEED_PERK(IN_PROGRESS, 2, "Unlock a Second Combat Speed Perk.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0)
                            npc("You still need to sacrifice your Combat Essence vouchers and unlock a second combat speed perk. This can be done at the Perk Master up north of Edgeville (Near the Lava Pool).");
                        else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_SECOND_COMBAT_SPEED_PERK");
                            npc("Excellent progress! Two combat speed perks unlocked! You're becoming a formidable warrior. Your next challenge awaits...");
                            npc("Now it is time to step it up... Your sixth objective is to Defeat TzTok-Jad and earn the Fire Cape! He can can be found at the Fight Caves (Minigame Teleport).");
                        }
                    }
                });
            }
        }
    },

    // Objective 6: Defeat Jad and get a Fire Cape
    DEFEAT_JAD_GET_FIRE_CAPE(IN_PROGRESS, 2, "Defeat Jad And Get A Fire Cape.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (player.isDebugging)
                            player.sendMessage("Progress: " + progress);
                        if (progress == 0) {
                            npc("Have you defeated TzTok-Jad and obtained the Fire Cape yet? Remember, TzTok-Jad can be found at the Fight Caves (Minigame Teleport). Defeat him and return to me when you're done!");
                        } else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "DEFEAT_JAD_GET_FIRE_CAPE");
                            npc("Remarkable! You've defeated TzTok-Jad and earned the Fire Cape! This is a feat that truly tests your ability to learn boss mechanics. Your combat skills are now among the elite!");
                            npc("Your seventh and final objective is to unlock the final combat speed perk to complete your training. I have given you another 100 Combat Essence Vouchers to help you purchase the last combat speed perk.");
                            final HintArrow arrow = new HintArrow(3088, 3509, (byte) 100, HintArrowPosition.CENTER);
                            player.getPacketDispatcher().sendHintArrow(arrow);
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            player.getInventory().addOrDrop(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 7: Unlock the final combat speed perk
    UNLOCK_FINAL_COMBAT_SPEED_PERK(IN_PROGRESS, 2, "Unlock The Final Combat Speed Perk.") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (progress == 0)
                            npc("You still need to sacrifice your Combat Essence vouchers and unlock a second combat speed perk. This can be done at the Perk Master up north of Edgeville (Near the Lava Pool).");
                        else {
                            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FINAL_COMBAT_SPEED_PERK");
                            npc("Outstanding! You've unlocked all three combat speed perks! Your mastery of combat speed is now complete. Take this final reward - you've truly earned it!").executeAction(() -> {
                                AdventurersPath.startQuest(player);
                            });
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            player.getInventory().addOrDrop(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.getInventory().addOrDrop(new Item(ItemId.COINS_995, 100000));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers and 100k GP!</col>");
        }
    },

    // Final - Quest Completion
    QUEST_COMPLETED(COMPLETED, 1, "Complete The Adventurer's Path") {
        @Override
        public int getDialogueNpc() { return NpcId.EXILES_GUIDE; }
        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final int progress = player.getQuestManager().getProgress(this);
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        QuestManager.updateQuest(player, AdventurersPath.class, "QUEST_COMPLETED");
                        npc("Congratulations, "+player.getName()+"! You have completed the Adventurer's Path quest!");
                        npc("You've proven yourself in combat, unlocked powerful perks, and conquered great challenges. You are now truly ready for whatever adventures await you in Exiles!");
                        npc("Go forth and conquer. Earn more Combat Essence to become even more powerful. Check out the Utility Essence perk system as well!");
                    }
                });
            }
        }
    };

    private final QuestStage stage;
    private final int objectiveLength;
    private final String[] objectiveDescription;
    private final Predicate<Player> predicate;

    public static final AdventurersPath[] VALUES = values();
    public static final Map<QuestStage, List<Quest>> MAP = new EnumMap<>(QuestStage.class);

    static {
        for (final QuestStage stage : QuestStage.VALUES) {
            MAP.put(stage, new ArrayList<>(15));
        }
        for (final AdventurersPath value : VALUES) {
            MAP.get(value.stage).add(value);
        }
    }

    AdventurersPath(final QuestStage stage, final String... objectiveDescription) {
        this(stage, 1, NO_PREDICATE, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final int objectiveLength, final String... objectiveDescription) {
        this(stage, objectiveLength, NO_PREDICATE, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final Predicate<Player> predicate, final String... objectiveDescription) {
        this(stage, 1, predicate, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final int objectiveLength, final Predicate<Player> predicate, final String... objectiveDescription) {
        this.stage = stage;
        this.objectiveLength = objectiveLength;
        this.predicate = predicate;
        this.objectiveDescription = objectiveDescription;
    }

    @Override
    public boolean flagging() {
        return false;
    }



    @Override
    public String title() {
        return "Adventurer's Path";
    }



    @Override
    public Map<QuestStage, List<Quest>> map() {
        return MAP;
    }

    @Override
    public QuestStage stage() {
        return stage;
    }

    @Override
    public int objectiveLength() {
        return objectiveLength;
    }

    @Override
    public String[] objectiveDescription() {
        return objectiveDescription;
    }

    @Override
    public Predicate<Player> predicate() {
        return predicate;
    }

    // ========================================
    // QUEST INTEGRATION METHODS
    // ========================================
    // These methods should be called from your existing game systems
    // to trigger quest progress updates automatically.
    /**
     * Call this when a player unlocks a combat speed perk
     * Integration point: Combat perk unlock system
     */
    public static void onCombatSpeedPerkUnlocked(Player player, int itemId) {
        if (AdventurersPath.getCurrentObjective(player) == AdventurersPath.UNLOCK_FIRST_COMBAT_SPEED_PERK && (CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MeleeISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("RangedISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MagicISpeed"))) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FIRST_COMBAT_SPEED_PERK");
            player.sendMessage("<col=ff0000>You have unlocked your first combat speed perk. Please return to the Exiles Guide.</col>");

        } else if (AdventurersPath.getCurrentObjective(player) == AdventurersPath.UNLOCK_SECOND_COMBAT_SPEED_PERK && (CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MeleeISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("RangedISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MagicISpeed"))) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_SECOND_COMBAT_SPEED_PERK");
            player.sendMessage("<col=ff0000>You have unlocked your second combat speed perk. Please return to the Exiles Guide.</col>");

        } else if (AdventurersPath.getCurrentObjective(player) == AdventurersPath.UNLOCK_FINAL_COMBAT_SPEED_PERK && (CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MeleeISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("RangedISpeed") || CombatPerkWrapper.get(itemId).getPerk().getSimpleName().equals("MagicISpeed"))) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FINAL_COMBAT_SPEED_PERK");
            player.sendMessage("<col=ff0000>You have unlocked your final combat speed perk. Please return to the Exiles Guide.</col>");
        }
    }

    /**
     * Call this when a player kills a Barrows brother
     * Integration point: Barrows brothers death system
     */
    public static void onBarrowsBrothersKilled(Player player, int brotherNpcId) {
        String attributeKey = "barrows_brother_killed_" + brotherNpcId;
        if (!player.getBooleanAttribute(attributeKey)) {
            player.addAttribute(attributeKey, 1);
            boolean allKilled = true;
            int[] brotherIds = {1672, 1673, 1674, 1675, 1676, 1677};
            for (int id : brotherIds) {
                if (!player.getBooleanAttribute("barrows_brother_killed_" + id)) {
                    allKilled = false;
                    break;
                }
            }
            if (allKilled) {
                QuestManager.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS");
                player.sendMessage("<col=ff0000>You have killed all the Barrows brothers! Please return to the Exiles Guide.</col>");
                for (int id : brotherIds) {
                    player.getAttributes().remove("barrows_brother_killed_" + id);
                }
            }
        }
    }

    /**
     * Call this when a player defeats Jad and receives a Fire Cape
     * Integration point: TzTok-Jad death system
     */
    public static void onJadDefeatedFireCapeReceived(Player player) {
        QuestManager.updateQuest(player, AdventurersPath.class, "DEFEAT_JAD_GET_FIRE_CAPE");
        player.sendMessage("<col=ff0000>You have defeated Jad and received a Fire Cape! Please return to the Exiles Guide.</col>");
    }

    /**
     * Check if player can start the Adventurer's Path quest
     */
    public static boolean canStartAdventurersPath(Player player) {
        return player.getBooleanAttribute("registered") &&
               player.getBooleanAttribute("received_starter") &&
               player.getQuestManager().getCurrentObjective("Adventurer's Path") == null;
    }

    /**
     * Check if the entire Adventurer's Path quest is completed
     * @param player the player
     * @return true if quest is fully completed
     */
    public static boolean isQuestCompleted(Player player) {
        return player.getQuestManager().isQuestCompleted("Adventurer's Path");
    }

    /**
     * Check if player can start the quest and send appropriate messages if they can't
     * @param player the player
     * @return true if player can start the quest, false otherwise
     */
    public static boolean canStartQuestWithMessages(Player player) {
        // Check if quest is already started or completed
        String currentObjective = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjective != null) {
            // Quest is in progress - don't show error, let dialogue handler deal with it
            return false;
        }

        // Check if player is registered
        if (!player.getBooleanAttribute("registered")) {
            player.sendMessage("<col=ff0000>You must be registered to start this quest.</col>");
            return false;
        }

        return true;
    }

    /**
     * Start the Adventurer's Path quest for a player
     * This method handles all requirements checking and quest starting, then redirects to the first objective's dialogue
     * @param player the player to start the quest for
     * @return true if quest was started successfully, false otherwise
     */
    public static boolean startQuest(Player player) {
        // Check if quest is already completed
        if (isQuestCompleted(player)) {
            player.sendMessage("<col=ff0000>You have already completed the Adventurer's Path quest!</col>");
            return false;
        }

        // Check if quest is in progress
        String currentObjectiveName = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjectiveName != null) {
            // Quest is in progress - show current objective dialogue if this NPC is involved
            Quest currentObjective = getCurrentObjective(player);
            if (currentObjective != null && currentObjective.getDialogueNpc() == NpcId.EXILES_GUIDE) {
                currentObjective.handleDialogue(player, NpcId.EXILES_GUIDE);
                return true;
            } else {
                player.sendMessage("<col=ff0000>You are currently working on a different objective. Check your quest log.</col>");
                return false;
            }
        }

        // Check requirements with messages
        if (!canStartQuestWithMessages(player)) {
            return false;
        }

        // Start the quest by setting the first objective as current
        player.getQuestManager().startQuest(AdventurersPath.TALK_TO_EXILES_GUIDE);

        // Send success message
        player.sendMessage("<col=00ff00>You have started the Adventurer's Path quest!</col>");

        // Redirect to the TALK_TO_EXILES_GUIDE objective's dialogue instead of duplicating it
        AdventurersPath.TALK_TO_EXILES_GUIDE.handleDialogue(player, NpcId.EXILES_GUIDE);

        return true;
    }

    /**
     * Gets the current objective (quest enum) for the player
     * @param player the player
     * @return current Quest objective or null if not found
     */
    public static Quest getCurrentObjective(Player player) {
        String currentObjectiveName = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjectiveName == null) {
            return null;
        }

        // Find the quest objective with the matching name
        for (AdventurersPath objective : AdventurersPath.VALUES) {
            if (objective.name().equals(currentObjectiveName)) {
                return objective;
            }
        }

        return null;
    }

    /**
     * Get quest progress summary for a player
     */
    public static String getQuestProgressSummary(Player player) {
        StringBuilder summary = new StringBuilder();
        summary.append("Adventurer's Path Progress:\n");

        for (AdventurersPath objective : AdventurersPath.VALUES) {
            boolean completed = player.getQuestManager().isCompleted(objective);
            String status = completed ? "[COMPLETED]" : "[INCOMPLETE]";
            summary.append(status).append(" ").append(String.join(" ", objective.objectiveDescription())).append("\n");
        }

        return summary.toString();
    }

    /**
     * Force complete a quest objective (for admin/testing purposes)
     */
    public static void forceCompleteQuestObjective(Player player, AdventurersPath objective) {
        if (player.isStaff()) {
            player.getQuestManager().finish(objective);
            player.sendMessage("Force completed quest objective: " + objective.name());
        }
    }

    /**
     * Reset quest progress (for admin/testing purposes)
     */
    public static void resetQuestProgress(Player player) {
        if (player.isStaff()) {
            for (AdventurersPath objective : AdventurersPath.VALUES) {
                player.getQuestManager().reset(objective);
            }

            // Clear Barrows brother tracking attributes
            int[] brotherIds = {1672, 1673, 1674, 1675, 1676, 1677};
            for (int id : brotherIds) {
                player.getAttributes().remove("barrows_brother_killed_" + id);
            }

            player.sendMessage("Quest progress has been reset.");
        }
    }
}
